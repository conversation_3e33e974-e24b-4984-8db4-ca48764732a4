import logging
from datetime import datetime
from pathlib import Path

import httpx
import polars as pl

from app.database.redis_client import get_credential, CredentialType, set_credential, del_credential
from app.utils.pdf_util import pdf_insert_text
from app.utils.play_api import play_api
from .delivery_notice_strategy import DeliveryNoticeStrategy, DeliveryNoticeFactory

logger = logging.getLogger(__name__)


__credential_login_name = 'LOGIN'
__credential_token_name = 'TOKEN'


async def fetch_credential():
    login_credential = await get_credential(CredentialType.ChengCheng004, __credential_login_name)
    assert login_credential, '长城登录账号未设置！'
    res = await play_api.post('/gwpst/login', json = {
        "credential": login_credential
    })
    res.raise_for_status()
    res_json = res.json()
    return res_json['value']


async def attach_credential(request: httpx.Request):
    token = await get_credential(CredentialType.ChengCheng004, __credential_token_name)
    if not token:
        token = await fetch_credential()
        await set_credential(CredentialType.ChengCheng004, __credential_token_name, token)
        logger.info(f'session refreshed')
    request.headers['api-token'] = token

async def retry_on_expired(response: httpx.Response):
    retry_attempt = response.request.extensions.get("retry_attempt", 0)

    request = response.request
    if retry_attempt < 1 and response.status_code == 401:
        request.extensions["retry_attempt"] = retry_attempt + 1
        await del_credential(CredentialType.ChengCheng004, __credential_token_name)
        logger.warning(f"session timeout, retrying request")
        new_response = await req_client.send(request)

        # Replace the response object's attributes with the new response's attributes
        response._content = new_response._content
        response.status_code = new_response.status_code
        response.headers = new_response.headers


__base_url = 'https://srm.gwpst.com/api/srm-purchase-execute/tenant/delivery/header/supplier'

req_client = httpx.AsyncClient(
    base_url=__base_url, 
    event_hooks={
        'request': [attach_credential],
        'response': [retry_on_expired]
    },
    timeout=120)


class ChangeChengStrategy(DeliveryNoticeStrategy):
    def get_column_mapping(self) -> dict:
        return {
            "F_PYZU_KHZL1": "交货明细序号",
            "CusOrderNo": "采购订单号",
            "NoteEntry": "采购订单行号",
            "F_PYZU_KHZL2": "工厂编码",
            "F_PYZU_KHZL3": "收货地点",
            "CusCargoCode": "物料号",
            "BoxSerialNo": "箱号条码",
            "PanSerialNo": "最小产品包装条码",
            "PCS": "最小包装数",
            "PCS2": "实际包装数",
            "today": "送货日期",
            "LotNo": "生产批次",
            "WMSDC_date": "生产日期",
            "OutstockNo": "备注"
        }

    async def get_wms_df(self) -> pl.DataFrame:
        df = await super().get_wms_df()
        df = df.with_columns(
            pl.col("PCS").alias("PCS2"),
            pl.lit(datetime.now().date()).alias("today"),
            pl.col("WMSDC").str.to_date("%Y%m%d").alias("WMSDC_date")
        )
        return df

    @staticmethod
    async def upload(self, excel_path: Path):
        files = {'excel': excel_path.read_bytes()}
        res_upload = await req_client.post('/import', files=files)
        res_upload.raise_for_status()
        file_info = res_upload.json()
        return file_info

    @staticmethod
    async def get_asn_no(self):
        res = await req_client.post('/page', json={"page":{"current":1,"size":2},"condition":"and","rules":[]})
        res_json = res.json()
        if res_json['code'] == 200:
            record = res_json['data']['records'][1]
            if record['asnStatus'] == 'CREATED':
                return record['asnNo'], ''
            elif record['asnStatus'] == 'CONFIRMED':
                return record['asnNo'], ''
            else:
                logger.info(f"送货单状态：{record['asnStatus']}", record)
                return '', record['asnStatus']
        # TODO
        return '', 'DRAFT'

    async def get_pdf_file(self, asn_no):
        res = await req_client.post('/print', json={
            'asnNo': asn_no
        })
        res.raise_for_status()
        pdf_path = self.get_cache_file('.pdf')
        pdf_path.write_bytes(res.content)
        return pdf_path

    def add_no_to_pdf(self, pdf_path):
        new_pdf_path = self.get_cache_file('_no.pdf')
        pdf_insert_text(pdf_path, new_pdf_path, self.outstock_info['BillNo'], (16, 30))
        return new_pdf_path

    async def run(self):
        excel_path = await self.get_excel()
        # file_info = await self.upload(excel_path)
        asn_no, msg = await self.get_asn_no()
        if asn_no:
            pdf_file = await self.get_pdf_file(asn_no)
            pdf_file = self.add_no_to_pdf(pdf_file)
            return pdf_file
        else:
            logger.info(msg)
        pass


DeliveryNoticeFactory.register_strategy('SZ-K21040063', ChangeChengStrategy)

