import hashlib
import json
import logging
import os
import time
from typing import List

import httpx
import jmespath

from app.database.redis_client import set_credential, CredentialType, get_credential, del_credential
from app.utils.app_config import app_config

logger = logging.getLogger(__name__)

KD_API_URL = 'http://k3c.jeelyton.net/k3cloud'


def get_sha256(arr: List[str]) -> str:

    # Sort array using ordinal (binary) comparison
    arr_sorted = sorted(arr, key=lambda s: s.encode('utf-8'))

    # Join strings without separator
    combined = ''.join(arr_sorted)

    # Compute SHA256 hash
    data = combined.encode('utf-8')
    hash_bytes = hashlib.sha256(data).digest()

    # Convert to hexadecimal string (uppercase)
    return hash_bytes.hex()

# https://vip.kingdee.com/knowledge/specialDetail/650386937144032256?category=650388108193709056&id=650369502764414208&type=Knowledge&productLineId=1&lang=zh-CN
def get_params_with_sign(uid: str):
    timestamp = str(int(time.time()))

    sign_params = [app_config.account_id, uid, app_config.app_id, app_config.app_secret, timestamp]

    sign = get_sha256(sign_params)
    return [ app_config.account_id, uid, app_config.app_id, timestamp, sign, 2052]

async def fetch_credential(uid: str):
    res_json = await login_by_sign(uid)
    session_id = res_json.get('KDSVCSessionId')
    assert session_id, res_json.get('Message', '登录出错！')
    return session_id


async def attach_credential(request: httpx.Request):
    print('hooks')
    uid = request.extensions.get('uid')
    assert uid, 'httpx extensions.uid is required'
    session_id = await get_credential(CredentialType.KingDee, uid)
    if not session_id:
        session_id = await fetch_credential(uid)
        await set_credential(CredentialType.KingDee, uid, session_id)
        logger.warning(f'kdsession refreshed for {uid}')
    request.headers['Cookie'] = f'kdservice-sessionid={session_id}'
    request.extensions['start_time'] = time.time()

async def retry_on_expired(response: httpx.Response):
    retry_attempt = response.request.extensions.get("retry_attempt", 0)
    
    # Read the response content first before accessing JSON
    await response.aread()

    response_json = response.json()
    request = response.request
    uid = request.extensions.get('uid')
    if retry_attempt < 1 and jmespath.search('Result.ResponseStatus.Errors[0].Message', response_json) == '会话信息已丢失，请重新登录':
        request.extensions["retry_attempt"] = retry_attempt + 1
        await del_credential(CredentialType.KingDee, uid)
        logger.warning(f"kdsession timeout, retrying request: {uid}")
        new_response = await kd_api.send(request)
        
        # Replace the response object's attributes with the new response's attributes
        response._content = new_response._content
        response.status_code = new_response.status_code
        response.headers = new_response.headers
    start_time = response.request.extensions.get('start_time')
    if start_time:
        duration = time.time() - start_time
        url = '.'.join(request.url.path.split('.')[-4:-2])
        logger.info(f"{url} {uid} {duration:.3f}s")



kd_api = httpx.AsyncClient(
    base_url=KD_API_URL,
    event_hooks={'request': [attach_credential], 'response': [retry_on_expired]},
    timeout=60
)

async def login_by_sign(uid: str):
    async with httpx.AsyncClient(base_url=KD_API_URL) as client:
        parameters = get_params_with_sign(uid)
        res = await client.post('/Kingdee.BOS.WebApi.ServicesStub.AuthService.LoginBySign.common.kdsvc', json = { "parameters": parameters })
        res.raise_for_status()
        return res.json()
async def login_by_password(name: str, password: str):
    async with httpx.AsyncClient(base_url=KD_API_URL) as client:
        res = await client.post('/Kingdee.BOS.WebApi.ServicesStub.AuthService.ValidateUser.common.kdsvc', json = {"parameters":[account_id, name, password,2052]})
        res.raise_for_status()
        return res.json()
