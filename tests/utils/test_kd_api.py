import async<PERSON>
from typing import Callable, <PERSON>wai<PERSON>, Optional, List

import httpx
import jmespath
from httpx import Request, Response, AsyncBaseTransport, AsyncHTTPTransport

from app.utils.kingd_api import KD_API_URL, attach_credential


class RetryLoginTransport(AsyncBaseTransport):
    def __init__(
        self,
        max_retries: int = 1,
        retry_status_codes: Optional[List[int]] = None,
        request_hooks: Optional[List[Callable[[Request], Awaitable[None]]]] = None
    ):
        self.max_retries = max_retries
        self.retry_status_codes = retry_status_codes or []
        self.request_hooks = request_hooks or []
        self._transport = AsyncHTTPTransport()

    async def handle_async_request(self, request: Request) -> Response:
        for retry in range(self.max_retries + 1):
            print(f'Attempt {retry + 1}/{self.max_retries + 1}')

            # Re-execute hooks on retry attempts (hooks are already executed on first attempt by httpx)
            if retry > 0:
                for hook in self.request_hooks:
                    await hook(request)

            response = await self._transport.handle_async_request(request)

            if retry < self.max_retries:
                should_retry = (response.status_code in self.retry_status_codes or
                              await self.should_retry_by_response(response))
                if should_retry:
                    print(f"Retrying request due to status {response.status_code} or session expired")
                    continue

            return response
        raise

    async def should_retry_by_response(self, response: Response):
        await response.aread()
        res_json = response.json()
        error_message = jmespath.search('Result.ResponseStatus.Errors[0].Message', res_json)
        if error_message == '会话信息已丢失，请重新登录':
            return True
        return False



kd_api = httpx.AsyncClient(
    base_url=KD_API_URL,
    transport=RetryLoginTransport(request_hooks=[attach_credential]),
    event_hooks={'request': [attach_credential]},
    timeout=60
)

async def run():
    res = await kd_api.post('/Kingdee.BOS.WebApi.ServicesStub.DynamicFormService.View.common.kdsvc', json={
        "data": {
            "CreateOrgId": 0,
            "Number": "SZ-STOA25074027.xlsx",
            "Id": ""
        },
        "formid": "SAL_DELIVERYNOTICE"
    }, extensions={'uid': '周雪玲'})
    print(11, res.json())

if __name__ == '__main__':
    asyncio.run(run())